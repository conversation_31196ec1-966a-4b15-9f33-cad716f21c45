'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import { Document, DocumentStatus, DocumentType, User } from '@/lib/types';
import { DocumentWorkflowStatus } from '@/components/documents/DocumentWorkflowStatus';
import { useAuth } from '@/hooks/useAuth';
import { useDocument, useDocumentQueries } from '@/hooks/useDocument';
import { AlertCircle, FileText, Upload, FolderOpen, Search, Filter, Clock, CheckCircle2, XCircle, Eye } from 'lucide-react';
import { useState, useMemo, useEffect } from 'react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from '@/components/ui/dialog';
import { useRouter, useSearchParams } from 'next/navigation';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { useToast } from '@/hooks/useToast';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useProject } from '@/hooks/useProject';
import { useUser } from '@/hooks/useUser';

const documentTypeLabels: Record<DocumentType, string> = {
  concept: 'Concept Paper',
  proposal: 'Thesis Proposal',
  thesis: 'Thesis',
};

const STATUS_COLORS: Record<DocumentStatus, string> = {
  approved: 'bg-green-500/10 text-green-500 hover:bg-green-500/20',
  rejected: 'bg-red-500/10 text-red-500 hover:bg-red-500/20',
  under_review: 'bg-yellow-500/10 text-yellow-500 hover:bg-yellow-500/20',
  draft: 'bg-gray-500/10 text-gray-500 hover:bg-gray-500/20',
};

const STATUS_ICONS: Record<DocumentStatus, React.ComponentType<{ className?: string }>> = {
  approved: CheckCircle2,
  rejected: XCircle,
  under_review: Clock,
  draft: FileText,
};

// Filter options for documents
const DOCUMENT_FILTERS = [
  { value: 'all', label: 'All Documents', icon: FileText },
  { value: 'under_review', label: 'Under Review', icon: Clock },
  { value: 'approved', label: 'Approved', icon: CheckCircle2 },
  { value: 'rejected', label: 'Rejected', icon: XCircle },
  { value: 'draft', label: 'Draft', icon: FileText },
] as const;

// Enhanced Document card component for better reusability
function DocumentCard({
  document,
  student,
  onClick,
  onDeleteClick,
  showStudent = false,
  showActions = true
}: {
  document: Document;
  student?: User;
  onClick: () => void;
  onDeleteClick?: (e: React.MouseEvent, id: string) => void;
  showStudent?: boolean;
  showActions?: boolean;
}) {
  const StatusIcon = STATUS_ICONS[document.status as DocumentStatus];

  return (
    <Card className="cursor-pointer hover:shadow-md transition-all duration-200 h-full">
      <CardContent className="p-4" onClick={onClick}>
        <div className="flex items-start justify-between mb-3">
          <div className="flex-1 min-w-0">
            <h3 className="font-semibold text-lg line-clamp-2 mb-2">{document.title}</h3>
            {showStudent && student && (
              <p className="text-sm text-muted-foreground mb-2">
                By {student.name}
              </p>
            )}
            <div className="flex items-center gap-2 mb-2">
              <Badge variant="outline" className="text-xs">
                {documentTypeLabels[document.type as DocumentType]}
              </Badge>
              <Badge
                variant={document.status === 'under_review' ? 'secondary' : 'outline'}
                className="text-xs flex items-center gap-1"
              >
                <StatusIcon className="h-3 w-3" />
                {document.status.replace('_', ' ')}
              </Badge>
            </div>
          </div>
          {onDeleteClick && showActions && (
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0 text-muted-foreground hover:text-destructive"
              onClick={(e) => {
                e.stopPropagation();
                onDeleteClick(e, document.id);
              }}
              aria-label="Delete document"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <path d="M3 6h18"></path>
                <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
                <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
              </svg>
            </Button>
          )}
        </div>

        <div className="space-y-2 text-sm text-muted-foreground">
          <div className="flex items-center justify-between">
            <span>Last Updated:</span>
            <span>{new Date(document.updatedAt).toLocaleDateString()}</span>
          </div>
          {document.reviewDue && (
            <div className="flex items-center justify-between">
              <span>Review Due:</span>
              <span className={new Date(document.reviewDue) < new Date() ? 'text-red-500' : ''}>
                {new Date(document.reviewDue).toLocaleDateString()}
              </span>
            </div>
          )}
        </div>

        {showActions && (
          <div className="flex items-center justify-between mt-3 pt-3 border-t">
            <Button
              variant="ghost"
              size="sm"
              className="text-xs"
              onClick={(e) => {
                e.stopPropagation();
                onClick();
              }}
            >
              <Eye className="h-3 w-3 mr-1" />
              View
            </Button>
            <DocumentWorkflowStatus
              currentStatus={document.status as DocumentStatus}
              compact={true}
            />
          </div>
        )}
      </CardContent>
    </Card>
  );
}

// Loading skeleton for documents
function DocumentsSkeleton() {
  return (
    <div className="dashboard-grid-3">
      {Array(6).fill(0).map((_, index) => (
        <div key={index} className="dashboard-card h-full">
          <div className="dashboard-card-header">
            <div className="flex items-center justify-between">
              <div className="flex-1">
                <Skeleton className="h-6 w-3/4" />
                <Skeleton className="h-5 w-1/4 mt-2" />
              </div>
              <Skeleton className="h-5 w-5 rounded" />
            </div>
          </div>
          <div className="dashboard-card-content">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <Skeleton className="h-4 w-1/4" />
                <Skeleton className="h-4 w-1/3" />
              </div>
              <div className="flex items-center justify-between">
                <Skeleton className="h-4 w-1/3" />
                <Skeleton className="h-4 w-1/4" />
              </div>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}

export default function DocumentsPage() {
  const { user } = useAuth();
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [documentToDelete, setDocumentToDelete] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const router = useRouter();
  const { toast } = useToast();
  const searchParams = useSearchParams();

  // Get filter from URL params
  const filterParam = searchParams.get('filter') || 'all';
  const [activeFilter, setActiveFilter] = useState<string>(filterParam);

  // Update filter when URL changes
  useEffect(() => {
    const urlFilter = searchParams.get('filter') || 'all';
    setActiveFilter(urlFilter);
  }, [searchParams]);

  // Get hooks for data fetching
  const { getList, delete: deleteDocument } = useDocument();
  const { useSupervisorDocuments, useDocumentsByStatus } = useDocumentQueries();
  const { useProjectsQuery } = useProject();
  const { useUsersByIdsQuery } = useUser();

  // Fetch data based on user role
  const isStudent = user?.role === 'student';
  const isSupervisor = user?.role === 'supervisor';
  const isManager = user?.role === 'manager';

  // For students - get their own documents
  const studentDocumentsQuery = getList(
    isStudent && user?.id ? { studentId: user.id } : undefined,
    {
      enabled: isStudent && !!user?.id,
      queryKey: ['documents', 'student', user?.id || '']
    }
  );

  // For supervisors - get documents from their assigned projects
  const supervisorProjectsQuery = useProjectsQuery(
    isSupervisor && user?.id ? { supervisorId: user.id } : undefined,
    {
      enabled: isSupervisor && !!user?.id,
      queryKey: ['projects', 'supervisor', user?.id || '']
    }
  );

  // Get all documents for supervisors/managers based on their projects
  const allDocumentsQuery = getList(
    (isSupervisor || isManager) ? {} : undefined,
    {
      enabled: (isSupervisor || isManager) && !!user?.id,
      queryKey: ['documents', 'all', user?.role || '']
    }
  );

  // Process documents based on user role and filter supervisor documents by their projects
  const rawDocuments = useMemo(() => {
    if (isStudent) {
      return studentDocumentsQuery.data || [];
    } else if (isSupervisor || isManager) {
      const allDocs = allDocumentsQuery.data || [];
      if (isSupervisor) {
        // Filter documents to only show those from supervisor's projects
        const supervisorProjects = supervisorProjectsQuery.data || [];
        const projectIds = supervisorProjects.map(p => p.id);
        return allDocs.filter(doc => projectIds.includes(doc.projectId));
      }
      return allDocs;
    }
    return [];
  }, [isStudent, isSupervisor, isManager, studentDocumentsQuery.data, allDocumentsQuery.data, supervisorProjectsQuery.data]);

  // Get unique student IDs for fetching student data
  const studentIds = useMemo(() => {
    return Array.from(new Set(rawDocuments.map(doc => doc.studentId)));
  }, [rawDocuments]);

  // Fetch student data for displaying names
  const studentsQuery = useUsersByIdsQuery(studentIds);
  const students = studentsQuery.data || [];

  // Filter and search documents
  const filteredDocuments = useMemo(() => {
    let filtered = [...rawDocuments];

    // Apply status filter
    if (activeFilter !== 'all') {
      filtered = filtered.filter(doc => doc.status === activeFilter);
    }

    // Apply search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(doc => {
        const student = students.find(s => s.id === doc.studentId);
        return (
          doc.title.toLowerCase().includes(query) ||
          doc.type.toLowerCase().includes(query) ||
          doc.status.toLowerCase().includes(query) ||
          (student?.name.toLowerCase().includes(query))
        );
      });
    }

    // Sort by last modified (newest first)
    return filtered.sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime());
  }, [rawDocuments, activeFilter, searchQuery, students]);

  // Loading and error states
  const isLoading = isStudent ? studentDocumentsQuery.isLoading :
                   (allDocumentsQuery.isLoading || supervisorProjectsQuery.isLoading);
  const isError = isStudent ? studentDocumentsQuery.isError :
                 (allDocumentsQuery.isError || supervisorProjectsQuery.isError);

  const handleDeleteClick = (e: React.MouseEvent, documentId: string) => {
    e.stopPropagation();
    setDocumentToDelete(documentId);
    setShowDeleteDialog(true);
  };

  const handleDeleteDocument = async () => {
    if (!documentToDelete) return;

    try {
      await deleteDocument.mutateAsync(documentToDelete);
      setShowDeleteDialog(false);
      setDocumentToDelete(null);
      toast({
        title: "Document deleted",
        description: "The document has been successfully deleted.",
      });
    } catch (error) {
      console.error('Error deleting document:', error);
      toast({
        title: "Error",
        description: "Failed to delete the document. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleFilterChange = (filter: string) => {
    setActiveFilter(filter);
    const params = new URLSearchParams(searchParams.toString());
    if (filter === 'all') {
      params.delete('filter');
    } else {
      params.set('filter', filter);
    }
    router.push(`/documents?${params.toString()}`);
  };

  // Get page title and description based on role and filter
  const getPageInfo = () => {
    if (isStudent) {
      return {
        title: 'My Documents',
        description: 'Manage your concept papers, proposals, and theses'
      };
    } else if (isSupervisor) {
      const filterInfo = DOCUMENT_FILTERS.find(f => f.value === activeFilter);
      return {
        title: activeFilter === 'under_review' ? 'Documents Under Review' :
               activeFilter === 'all' ? 'All Supervised Documents' :
               `${filterInfo?.label || 'Documents'}`,
        description: activeFilter === 'under_review' ?
                    'Review and provide feedback on student submissions' :
                    'Manage documents from your supervised projects'
      };
    } else {
      return {
        title: 'All Documents',
        description: 'Manage all documents in the system'
      };
    }
  };

  const pageInfo = getPageInfo();

  // Loading state with skeleton
  if (isLoading) {
    return (
      <div className="py-2">
        <div className="flex items-center justify-between mb-6">
          <div>
            <Skeleton className="h-9 w-48" />
            <Skeleton className="h-5 w-72 mt-1" />
          </div>
          <Skeleton className="h-10 w-32" />
        </div>
        <DocumentsSkeleton />
      </div>
    );
  }

  // Error state
  if (isError) {
    return (
      <div className="py-2">
        <Alert variant="destructive" className="mb-6">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>
            There was an error loading documents. Please try again later.
          </AlertDescription>
        </Alert>
        <Button onClick={() => window.location.reload()} variant="outline">
          Retry
        </Button>
      </div>
    );
  }

  // Empty state
  if (filteredDocuments.length === 0) {
    const hasDocuments = rawDocuments.length > 0;
    const isFiltered = activeFilter !== 'all' || searchQuery.trim() !== '';

    return (
      <div className="py-2">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-6">
          <div>
            <h1 className="dashboard-title">{pageInfo.title}</h1>
            <p className="dashboard-subtitle">{pageInfo.description}</p>
          </div>
          {isStudent && (
            <Button
              onClick={() => router.push('/documents/new')}
              className="dashboard-button-primary"
            >
              <Upload className="mr-2 h-4 w-4" />
              <span>New Document</span>
            </Button>
          )}
        </div>

        {/* Filters and Search - only show if there are documents or active filters */}
        {(hasDocuments || isFiltered) && (
          <div className="flex flex-col sm:flex-row gap-4 mb-6">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search documents..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
            <div className="flex gap-2 overflow-x-auto">
              {DOCUMENT_FILTERS.map((filter) => {
                const Icon = filter.icon;
                return (
                  <Button
                    key={filter.value}
                    variant={activeFilter === filter.value ? "default" : "outline"}
                    size="sm"
                    onClick={() => handleFilterChange(filter.value)}
                    className="flex items-center gap-2 whitespace-nowrap"
                  >
                    <Icon className="h-4 w-4" />
                    {filter.label}
                  </Button>
                );
              })}
            </div>
          </div>
        )}

        {/* Empty state message */}
        <div className="flex flex-col items-center justify-center py-12 text-center">
          <div className="rounded-full bg-muted p-3 mb-4">
            <FolderOpen className="h-10 w-10 text-muted-foreground" />
          </div>
          <h3 className="text-lg font-semibold mb-1">
            {isFiltered ? 'No documents found' :
             isStudent ? 'No documents yet' : 'No documents available'}
          </h3>
          <p className="text-muted-foreground mb-4 max-w-md">
            {isFiltered ? 'Try adjusting your search or filter criteria.' :
             isStudent ? "You haven't created any documents yet. Start by creating a new document." :
             "No documents are available for review at this time."}
          </p>
          {isStudent && !isFiltered && (
            <Button
              onClick={() => router.push('/documents/new')}
              className="flex items-center"
            >
              <Upload className="mr-2 h-4 w-4" />
              <span>New Document</span>
            </Button>
          )}
        </div>
      </div>
    );
  }

  // Main content with documents
  return (
    <div className="py-2">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-6">
        <div>
          <h1 className="dashboard-title">{pageInfo.title}</h1>
          <p className="dashboard-subtitle">{pageInfo.description}</p>
        </div>
        {isStudent && (
          <Button
            onClick={() => router.push('/documents/new')}
            className="dashboard-button-primary"
          >
            <Upload className="mr-2 h-4 w-4" />
            <span>New Document</span>
          </Button>
        )}
      </div>

      {/* Filters and Search */}
      <div className="flex flex-col sm:flex-row gap-4 mb-6">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search documents..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
        <div className="flex gap-2 overflow-x-auto">
          {DOCUMENT_FILTERS.map((filter) => {
            const Icon = filter.icon;
            const count = rawDocuments.filter(doc =>
              filter.value === 'all' ? true : doc.status === filter.value
            ).length;

            return (
              <Button
                key={filter.value}
                variant={activeFilter === filter.value ? "default" : "outline"}
                size="sm"
                onClick={() => handleFilterChange(filter.value)}
                className="flex items-center gap-2 whitespace-nowrap"
              >
                <Icon className="h-4 w-4" />
                {filter.label}
                <Badge variant="secondary" className="ml-1 text-xs">
                  {count}
                </Badge>
              </Button>
            );
          })}
        </div>
      </div>

      {/* Documents Grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {filteredDocuments.map((doc) => {
          const student = students.find(s => s.id === doc.studentId);
          return (
            <DocumentCard
              key={doc.id}
              document={doc}
              student={student}
              onClick={() => router.push(`/documents/${doc.id}`)}
              onDeleteClick={isStudent ? handleDeleteClick : undefined}
              showStudent={!isStudent}
              showActions={true}
            />
          );
        })}
      </div>

      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Document</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this document? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowDeleteDialog(false)}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteDocument}
            >
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}








