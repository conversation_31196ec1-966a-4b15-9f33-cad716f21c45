"use client";

import { useState, useEffect, useMemo, useCallback } from "react";
import { useAuth } from "@/hooks/useAuth";
import { useRouter } from "next/navigation";
import { useAnalytics } from "@/hooks/useAnalytics";
import { Header } from "@/components/layout/Header";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

import { Badge } from "@/components/ui/badge";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { ErrorBoundary } from "@/components/ErrorBoundary";
import { StudentAnalyticsView } from "@/components/analytics/StudentAnalyticsView";
import { SupervisorAnalyticsView } from "@/components/analytics/SupervisorAnalyticsView";
import { ManagerAnalyticsView } from "@/components/analytics/ManagerAnalyticsView";
import {
  BarChart3,
  TrendingUp,
  Users,
  BookOpen,
  AlertTriangle,
  Info,
  RefreshCw,
} from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { cn } from "@/lib/utils";
import { toast } from "@/hooks/useToast";

type TimeRange = "month" | "quarter" | "year";

interface RoleConfig {
  title: string;
  description: string;
  icon: React.ReactNode;
  defaultTimeRange: TimeRange;
  availableTimeRanges: TimeRange[];
}

interface AnalyticsPageState {
  timeRange: TimeRange;
  isRefreshing: boolean;
  lastRefresh: Date | null;
}

// Loading skeleton component for better UX
const AnalyticsSkeleton = () => (
  <div className="space-y-6">
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      {Array.from({ length: 4 }).map((_, i) => (
        <Card key={i} className="dashboard-card">
          <CardContent className="p-6">
            <div className="dashboard-skeleton h-4 w-20 mb-2" />
            <div className="dashboard-skeleton h-8 w-16 mb-2" />
            <div className="dashboard-skeleton h-3 w-24" />
          </CardContent>
        </Card>
      ))}
    </div>
    <div className="grid gap-6 md:grid-cols-2">
      {Array.from({ length: 2 }).map((_, i) => (
        <Card key={i} className="dashboard-card">
          <CardHeader>
            <div className="dashboard-skeleton h-5 w-32 mb-2" />
            <div className="dashboard-skeleton h-4 w-48" />
          </CardHeader>
          <CardContent>
            <div className="dashboard-skeleton h-64 w-full" />
          </CardContent>
        </Card>
      ))}
    </div>
  </div>
);

export default function AnalysisPage() {
  const { user: currentUser } = useAuth();
  const router = useRouter();
  const [state, setState] = useState<AnalyticsPageState>({
    timeRange: "quarter",
    isRefreshing: false,
    lastRefresh: null,
  });

  // Get analytics data and refresh functionality
  const {
    isLoading: analyticsLoading,
    error: analyticsError,
    refreshAnalytics: refreshAnalyticsData,
    isRefetching,
    lastUpdated,
  } = useAnalytics({
    timeRange: state.timeRange,
    enableRealTime: true,
    includeExtendedAnalytics: true,
    filters: {
      userRole: currentUser?.role === 'manager' || currentUser?.role === 'admin'
        ? 'all'
        : currentUser?.role || 'student'
    }
  });

  // Memoized role configuration to prevent unnecessary re-renders
  // Note: We need to handle the case where currentUser might be null
  const roleConfig = useMemo((): RoleConfig => {
    if (!currentUser) {
      return {
        title: "Analytics Dashboard",
        description: "Please log in to access analytics",
        icon: <TrendingUp className="h-6 w-6" />,
        defaultTimeRange: "month",
        availableTimeRanges: ["month"],
      };
    }

    switch (currentUser.role) {
      case 'student':
        return {
          title: "My Academic Analytics",
          description: "Track your academic progress, project status, and performance metrics",
          icon: <BookOpen className="h-6 w-6" />,
          defaultTimeRange: "month",
          availableTimeRanges: ["month", "quarter"],
        };
      case 'supervisor':
        return {
          title: "Supervision Analytics",
          description: "Monitor your supervised projects, student progress, and review workload",
          icon: <Users className="h-6 w-6" />,
          defaultTimeRange: "quarter",
          availableTimeRanges: ["month", "quarter", "year"],
        };
      case 'manager':
      case 'admin':
        return {
          title: "Comprehensive Analytics Dashboard",
          description: "Complete organizational overview with detailed insights and reports",
          icon: <BarChart3 className="h-6 w-6" />,
          defaultTimeRange: "year",
          availableTimeRanges: ["month", "quarter", "year"],
        };
      default:
        return {
          title: "Analytics Dashboard",
          description: "View your analytics and insights",
          icon: <TrendingUp className="h-6 w-6" />,
          defaultTimeRange: "month",
          availableTimeRanges: ["month"],
        };
    }
  }, [currentUser?.role]);

  // Initialize time range based on role configuration
  useEffect(() => {
    if (!roleConfig.availableTimeRanges.includes(state.timeRange)) {
      setState(prev => ({
        ...prev,
        timeRange: roleConfig.defaultTimeRange,
      }));
    }
  }, [roleConfig.defaultTimeRange, roleConfig.availableTimeRanges, state.timeRange]);

  // Handle time range changes
  const handleTimeRangeChange = useCallback((newTimeRange: TimeRange) => {
    setState(prev => ({
      ...prev,
      timeRange: newTimeRange,
    }));
  }, []);

  // Handle analytics refresh
  const handleRefresh = useCallback(async () => {
    setState(prev => ({ ...prev, isRefreshing: true }));
    try {
      await refreshAnalyticsData();
      setState(prev => ({
        ...prev,
        isRefreshing: false,
        lastRefresh: new Date()
      }));
      toast({
        title: "Analytics Refreshed",
        description: "Your analytics data has been updated successfully.",
      });
    } catch (error) {
      setState(prev => ({ ...prev, isRefreshing: false }));
      toast({
        title: "Refresh Failed",
        description: "Failed to refresh analytics data. Please try again.",
        variant: "destructive",
      });
    }
  }, [refreshAnalyticsData]);

  // Memoized analytics component renderer
  const renderRoleSpecificAnalytics = useMemo(() => {
    if (!currentUser) {
      return (
        <Card className="dashboard-card">
          <CardContent className="p-6">
            <div className="text-center text-muted-foreground">
              <AlertTriangle className="h-8 w-8 mx-auto mb-2" />
              <p>Please log in to view analytics</p>
            </div>
          </CardContent>
        </Card>
      );
    }

    const commonProps = { timeRange: state.timeRange };

    switch (currentUser.role) {
      case 'student':
        return <StudentAnalyticsView {...commonProps} />;
      case 'supervisor':
        return <SupervisorAnalyticsView {...commonProps} />;
      case 'manager':
      case 'admin':
        return <ManagerAnalyticsView {...commonProps} />;
      default:
        return (
          <Card className="dashboard-card">
            <CardContent className="p-6">
              <div className="text-center text-muted-foreground">
                <AlertTriangle className="h-8 w-8 mx-auto mb-2" />
                <p>Analytics not available for your role</p>
                <p className="text-sm mt-2">
                  Please contact your administrator for access.
                </p>
              </div>
            </CardContent>
          </Card>
        );
    }
  }, [currentUser?.role, state.timeRange]);

  // Check if user has permission to access analysis
  if (!currentUser) {
    return (
      <div className="min-h-screen bg-background flex flex-col">
        <Header />
        <div className="flex-1 flex items-center justify-center">
          <Card className="w-full max-w-md">
            <CardHeader>
              <CardTitle>Authentication Required</CardTitle>
              <CardDescription>
                Please log in to access the analysis dashboard.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button onClick={() => router.push("/auth/login")}>
                Go to Login
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <ErrorBoundary>
      <div className="min-h-screen bg-background flex flex-col">
        <Header />
        <div className="flex-1 container py-6 space-y-6">
          {/* Enhanced Page Header */}
          <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-primary/10 rounded-lg">
                {roleConfig.icon}
              </div>
              <div>
                <h1 className="text-3xl font-bold tracking-tight">{roleConfig.title}</h1>
                <p className="text-muted-foreground">{roleConfig.description}</p>
                {(state.lastRefresh || lastUpdated) && (
                  <p className="text-xs text-muted-foreground mt-1">
                    Last updated: {(state.lastRefresh || lastUpdated)?.toLocaleTimeString()}
                  </p>
                )}
              </div>
            </div>
            <div className="flex items-center gap-2 flex-wrap">
              {/* Enhanced Time Range Filter */}
              <div className="flex items-center gap-2">
                <span className="text-sm text-muted-foreground hidden sm:inline">Filter:</span>
                <Select
                  value={state.timeRange}
                  onValueChange={handleTimeRangeChange}
                  disabled={state.isRefreshing || isRefetching}
                >
                  <SelectTrigger className="w-[180px] bg-background border-input" aria-label="Select time range">
                    <SelectValue placeholder="Select time range" />
                  </SelectTrigger>
                  <SelectContent className="z-50">
                    {roleConfig.availableTimeRanges.length > 0 ? (
                      roleConfig.availableTimeRanges.map((range) => (
                        <SelectItem key={range} value={range}>
                          {range === "month" && "Last Month"}
                          {range === "quarter" && "Last Quarter"}
                          {range === "year" && "Last Year"}
                        </SelectItem>
                      ))
                    ) : (
                      <SelectItem value="month" disabled>
                        No time ranges available
                      </SelectItem>
                    )}
                  </SelectContent>
                </Select>
              </div>

              <Badge variant="secondary" className="text-sm">
                {currentUser.role.charAt(0).toUpperCase() + currentUser.role.slice(1)}
              </Badge>
            </div>
          </div>

          {/* Enhanced Role-specific Information Alert */}
          <Alert className="dashboard-card border-l-4 border-l-primary">
            <Info className="h-4 w-4" />
            <AlertTitle>Analytics Scope</AlertTitle>
            <AlertDescription>
              {currentUser.role === 'student' &&
                "You can view analytics for your own projects, documents, and academic progress."
              }
              {currentUser.role === 'supervisor' &&
                "You can view analytics for all projects you supervise and students under your guidance."
              }
              {(currentUser.role === 'manager' || currentUser.role === 'admin') &&
                "You have access to comprehensive analytics across all projects, users, and organizational metrics."
              }
            </AlertDescription>
          </Alert>

          {/* Analytics Content */}
          <div className="space-y-6">
            {(state.isRefreshing || analyticsLoading || isRefetching) ? (
              <AnalyticsSkeleton />
            ) : analyticsError ? (
              <Card className="dashboard-card border-destructive/50 bg-destructive/5">
                <CardContent className="p-8">
                  <div className="text-center">
                    <AlertTriangle className="h-12 w-12 mx-auto mb-4 text-destructive" />
                    <h3 className="text-lg font-semibold mb-2">Failed to Load Analytics</h3>
                    <p className="text-muted-foreground mb-4">
                      {analyticsError || 'Unable to load analytics data. Please try refreshing.'}
                    </p>
                    <Button variant="outline" onClick={handleRefresh} disabled={state.isRefreshing || isRefetching}>
                      <RefreshCw className={cn("h-4 w-4 mr-2", (state.isRefreshing || isRefetching) && "animate-spin")} />
                      Try Again
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ) : (
              renderRoleSpecificAnalytics
            )}
          </div>
        </div>
      </div>
    </ErrorBoundary>
  );
}
