import { storage, databases, APPWRITE_CONFIG } from ".";
import { Document, DocumentStatus, DocumentType } from "@/lib/types";
import { ID, Query, AppwriteException, Models } from "appwrite";
import { withRetry } from "@/lib/utils";
import { handleAppwriteError } from "@/lib/utils/error-handling";
import { mapBaseDocument } from "@/lib/utils/appwrite";

export class DocumentApiError extends Error {
  constructor(message: string, public originalError: unknown) {
    super(message);
    this.name = "DocumentApiError";
  }
}

// Map document from Appwrite to our Document type
function mapDocumentData(doc: any): Document {
  return {
    ...mapBaseDocument<Omit<Document, "id" | "createdAt" | "updatedAt">>(doc),
    title: doc.title,
    type: doc.type,
    status: doc.status,
    studentId: doc.studentId,
    projectId: doc.projectId,
    content: doc.content,
    comments: doc.comments,
    supervisorIds: doc.supervisorIds,
    fileUrl: doc.fileUrl,
    reviewDue: doc.reviewDue,
    lastModified: doc.lastModified,
  };
}

export async function createDocument(
  document: Omit<Document, "id" | "createdAt" | "updatedAt">
): Promise<Document> {
  return withRetry(async () => {
    try {
      const documentData = {
        type: document.type,
        title: document.title,
        status: document.status || "draft",
        content: document.content || "",
        studentId: document.studentId,
        supervisorIds: document.supervisorIds || [],
        projectId: document.projectId,
        fileUrl: document.fileUrl,
        reviewDue: document.reviewDue,
        lastModified: new Date().toISOString(),
      };

      const doc = await databases.createDocument(
        APPWRITE_CONFIG.databaseId,
        APPWRITE_CONFIG.collections.documents,
        ID.unique(),
        documentData
      );



      return mapDocumentData(doc);
    } catch (error) {
      throw handleAppwriteError(error, "Failed to create document");
    }
  });
}
export async function getDocuments(): Promise<Document[]> {
  return withRetry(async () => {
    try {
      const response = await databases.listDocuments(
        APPWRITE_CONFIG.databaseId,
        APPWRITE_CONFIG.collections.documents,
        [Query.limit(100)]
      );

      // Get documents with their comments
      const documentsWithComments = await Promise.all(
        response.documents.map(async (doc) => {
          // Fetch comments for this document
          const commentsResponse = await databases.listDocuments(
            APPWRITE_CONFIG.databaseId,
            APPWRITE_CONFIG.collections.comments,
            [Query.equal("documentId", doc.$id)]
          );

          // Map document and include comment IDs
          const documentData = mapDocumentData(doc);
          documentData.comments = commentsResponse.documents.map(
            (comment) => comment.$id
          );

          return documentData;
        })
      );

      return documentsWithComments;
    } catch (error) {
      console.error("Error fetching all documents:", error);
      throw handleAppwriteError(error, "Failed to fetch documents");
    }
  });
}
export async function getDocument(documentId: string): Promise<Document> {
  return withRetry(async () => {
    try {
      const doc = await databases.getDocument(
        APPWRITE_CONFIG.databaseId,
        APPWRITE_CONFIG.collections.documents,
        documentId
      );

      // Fetch comments for this document
      const commentsResponse = await databases.listDocuments(
        APPWRITE_CONFIG.databaseId,
        APPWRITE_CONFIG.collections.comments,
        [Query.equal("documentId", documentId)]
      );

      // Map document data and include comments
      const documentData = mapDocumentData(doc);
      documentData.comments = commentsResponse.documents.map(
        (comment) => comment.$id
      );

      return documentData;
    } catch (error) {
      throw handleAppwriteError(error, "Error fetching document");
    }
  });
}

export async function updateDocument(
  documentId: string,
  updates: Partial<Document>,
  options?: {
    notifyParticipants?: boolean;
    updateStatus?: boolean;
  }
): Promise<Document> {
  return withRetry(async () => {
    try {
      // Get the current document to compare changes
      const currentDoc = await databases.getDocument(
        APPWRITE_CONFIG.databaseId,
        APPWRITE_CONFIG.collections.documents,
        documentId
      );

      // Map the current document to our Document type
      const currentDocument = mapDocumentData(currentDoc);

      // Update the document with new data
      const doc = await databases.updateDocument(
        APPWRITE_CONFIG.databaseId,
        APPWRITE_CONFIG.collections.documents,
        documentId,
        {
          ...updates,
          lastModified: new Date().toISOString(),
        }
      );

      // Always notify participants about document updates
      await notifyDocumentParticipants(documentId, currentDoc.title, updates);

      // Handle optional operations based on provided options
      if (options?.notifyParticipants) {
        await notifyDocumentParticipants(documentId, currentDoc.title, updates);
      }

      return mapDocumentData(doc);
    } catch (error) {
      throw handleAppwriteError(error, "Error updating document");
    }
  });
}

export async function getDocumentsByStudentId(
  studentId: string
): Promise<Document[]> {
  return withRetry(async () => {
    try {
      const response = await databases.listDocuments(
        APPWRITE_CONFIG.databaseId,
        APPWRITE_CONFIG.collections.documents,
        [Query.equal("studentId", studentId)]
      );

      // Get documents with their comments
      const documentsWithComments = await Promise.all(
        response.documents.map(async (doc) => {
          // Fetch comments for this document
          const commentsResponse = await databases.listDocuments(
            APPWRITE_CONFIG.databaseId,
            APPWRITE_CONFIG.collections.comments,
            [Query.equal("documentId", doc.$id)]
          );

          // Map document and include comment IDs
          const documentData = mapDocumentData(doc);
          documentData.comments = commentsResponse.documents.map(
            (comment) => comment.$id
          );

          return documentData;
        })
      );

      return documentsWithComments;
    } catch (error) {
      console.error("Error fetching student documents:", error);
      throw handleAppwriteError(
        error,
        `Failed to fetch documents for student ${studentId}`
      );
    }
  });
}

export async function getDocumentsBySupervisorId(
  supervisorId: string
): Promise<Document[]> {
  return withRetry(async () => {
    try {
      const response = await databases.listDocuments(
        APPWRITE_CONFIG.databaseId,
        APPWRITE_CONFIG.collections.documents,
        [Query.equal("supervisorIds", supervisorId)]
      );

      // Get documents with their comments
      const documentsWithComments = await Promise.all(
        response.documents.map(async (doc) => {
          // Fetch comments for this document
          const commentsResponse = await databases.listDocuments(
            APPWRITE_CONFIG.databaseId,
            APPWRITE_CONFIG.collections.comments,
            [Query.equal("documentId", doc.$id)]
          );

          // Map document and include comment IDs
          const documentData = mapDocumentData(doc);
          documentData.comments = commentsResponse.documents.map(
            (comment) => comment.$id
          );

          return documentData;
        })
      );

      return documentsWithComments;
    } catch (error) {
      console.error("Error fetching supervisor documents:", error);
      throw handleAppwriteError(
        error,
        `Failed to fetch documents for supervisor ${supervisorId}`
      );
    }
  });
}

export async function getDocumentsByStatus(
  status: string
): Promise<Document[]> {
  return withRetry(async () => {
    try {
      const response = await databases.listDocuments(
        APPWRITE_CONFIG.databaseId,
        APPWRITE_CONFIG.collections.documents,
        [Query.equal("status", status)]
      );

      // Get documents with their comments
      const documentsWithComments = await Promise.all(
        response.documents.map(async (doc) => {
          // Fetch comments for this document
          const commentsResponse = await databases.listDocuments(
            APPWRITE_CONFIG.databaseId,
            APPWRITE_CONFIG.collections.comments,
            [Query.equal("documentId", doc.$id)]
          );

          // Map document and include comment IDs
          const documentData = mapDocumentData(doc);
          documentData.comments = commentsResponse.documents.map(
            (comment) => comment.$id
          );

          return documentData;
        })
      );

      return documentsWithComments;
    } catch (error) {
      console.error(`Error fetching documents with status ${status}:`, error);
      throw handleAppwriteError(
        error,
        `Failed to fetch documents with status ${status}`
      );
    }
  });
}

export async function uploadDocument(
  file: File,
  metadata: {
    [x: string]: any;
    type: DocumentType;
    title: string;
    projectId: string;
    studentId: string;
    supervisorIds: string[];
    status?: DocumentStatus;
  },
  onProgress?: (progress: number) => void
): Promise<Document> {
  return withRetry(async () => {
    try {
      // Validate projectId
      if (!metadata.projectId) {
        throw new Error("Project ID is required");
      }

      // Validate file is a PDF
      const fileType = file.type.toLowerCase();
      const fileExtension = file.name.split('.').pop()?.toLowerCase();

      if (fileType !== 'application/pdf' && fileExtension !== 'pdf') {
        throw new Error("Only PDF files are accepted for document uploads");
      }

      // 1. Upload file to storage
      const fileId = ID.unique();

      if (onProgress) {
        storage.client.subscribe(
          `files.${APPWRITE_CONFIG.buckets.documents}.${fileId}`,
          (response) => {
            const progress = response.payload as number;
            onProgress(progress / 100);
          }
        );
      }

      const uploadedFile = await storage.createFile(
        APPWRITE_CONFIG.buckets.documents,
        fileId,
        file
      );

      // 2. Create document record in database
      const documentStatus = metadata.status || "draft";
      const document = await databases.createDocument(
        APPWRITE_CONFIG.databaseId,
        APPWRITE_CONFIG.collections.documents,
        ID.unique(),
        {
          type: metadata.type,
          title: metadata.title,
          status: documentStatus,
          studentId: metadata.studentId,
          supervisorIds: metadata.supervisorIds,
          projectId: metadata.projectId,
          content: metadata.content,
          fileUrl: storage.getFileView(APPWRITE_CONFIG.buckets.documents, uploadedFile.$id),
          reviewDue: null,
          lastModified: new Date().toISOString(),
        }
      );

      // 3. Send notifications to managers and student (only for non-draft documents)
      if (documentStatus !== 'draft') {
        await sendDocumentNotifications(
          document.$id,
          metadata.title,
          metadata.projectId,
          metadata.studentId
        );
      }

      return mapDocumentData(document);
    } catch (error) {
      throw handleAppwriteError(error, "Failed to upload document");
    }
  });
}

/**
 * Helper function to extract file ID from a file URL
 * @param fileUrl - The file URL from Appwrite storage
 * @returns The file ID or null if not found
 */
function extractFileIdFromUrl(fileUrl: string): string | null {
  if (!fileUrl) return null;

  try {
    // Appwrite file URLs follow this pattern:
    // https://cloud.appwrite.io/v1/storage/buckets/{bucketId}/files/{fileId}/view
    const urlParts = fileUrl.split('/');

    // Method 1: Find by position relative to 'view'
    const viewIndex = urlParts.findIndex(part => part === 'view');
    if (viewIndex > 1) {
      return urlParts[viewIndex - 1];
    }

    // Method 2: Find by position relative to 'files'
    const filesIndex = urlParts.findIndex(part => part === 'files');
    if (filesIndex >= 0 && filesIndex + 1 < urlParts.length) {
      return urlParts[filesIndex + 1];
    }

    // Method 3: Try to extract from URL using regex
    const fileIdMatch = fileUrl.match(/\/files\/([^\/]+)/);
    if (fileIdMatch && fileIdMatch[1]) {
      return fileIdMatch[1];
    }

    console.warn('Could not extract file ID from URL:', fileUrl);
    return null;
  } catch (error) {
    console.error('Error extracting file ID from URL:', error);
    return null;
  }
}

export async function deleteDocument(documentId: string): Promise<void> {
  return withRetry(async () => {
    try {
      // First, get the document to check if it has an associated file
      const document = await databases.getDocument(
        APPWRITE_CONFIG.databaseId,
        APPWRITE_CONFIG.collections.documents,
        documentId
      );

      // Delete the file from storage if it exists
      if (document.fileUrl) {
        const fileId = extractFileIdFromUrl(document.fileUrl);
        if (fileId) {
          try {
            await storage.deleteFile(
              APPWRITE_CONFIG.buckets.documents,
              fileId
            );
            console.log(`Deleted file ${fileId} from storage for document ${documentId}`);
          } catch (fileError) {
            console.error(`Error deleting file for document ${documentId}:`, fileError);
            // Continue with document deletion even if file deletion fails
          }
        }
      }

      // Next, delete all comments associated with this document
      const commentsResponse = await databases.listDocuments(
        APPWRITE_CONFIG.databaseId,
        APPWRITE_CONFIG.collections.comments,
        [Query.equal("documentId", documentId)]
      );

      // Delete all comments in parallel
      await Promise.all(
        commentsResponse.documents.map(comment =>
          databases.deleteDocument(
            APPWRITE_CONFIG.databaseId,
            APPWRITE_CONFIG.collections.comments,
            comment.$id
          )
        )
      );

      // Finally delete the document itself
      await databases.deleteDocument(
        APPWRITE_CONFIG.databaseId,
        APPWRITE_CONFIG.collections.documents,
        documentId
      );
    } catch (error) {
      throw handleAppwriteError(error, "Failed to delete document and comments");
    }
  });
}

// Helper function to send notifications
async function sendDocumentNotifications(
  documentId: string,
  documentTitle: string,
  projectId: string,
  studentId: string
): Promise<void> {
  try {
    // 1. Get project title
    const project = await databases.getDocument(
      APPWRITE_CONFIG.databaseId,
      APPWRITE_CONFIG.collections.projects,
      projectId
    );

    // 2. Get all users with manager role
    const managersResponse = await databases.listDocuments(
      APPWRITE_CONFIG.databaseId,
      APPWRITE_CONFIG.collections.profiles,
      [Query.equal("role", "manager")]
    );

    // 3. Create notification for each manager and the student
    const notifications = [
      // Notifications for managers
      ...managersResponse.documents.map((manager) => ({
        userId: manager.$id,
        type: "submission",
        title: "New Document Submitted",
        message: `A new document "${documentTitle}" was submitted for project "${project.title}"`,
        read: false,
        relatedId: documentId,
        relatedType: "document",
      })),
      // Notification for the student
      {
        userId: studentId,
        type: "submission",
        title: "Document Submitted Successfully",
        message: `Your document "${documentTitle}" for project "${project.title}" was submitted successfully`,
        read: false,
        relatedId: documentId,
        relatedType: "document",
      },
    ];

    // Create all notifications in parallel
    await Promise.all(
      notifications.map((notification) =>
        databases.createDocument(
          APPWRITE_CONFIG.databaseId,
          APPWRITE_CONFIG.collections.notifications,
          ID.unique(),
          notification
        )
      )
    );
  } catch (error) {
    console.error("Error sending notifications:", error);
    // Don't throw here to prevent document upload failure
  }
}

// Implementation for notifying document participants
async function notifyDocumentParticipants(
  documentId: string,
  documentTitle: string,
  updates: Partial<Document>
): Promise<void> {
  try {
    const doc = await databases.getDocument(
      APPWRITE_CONFIG.databaseId,
      APPWRITE_CONFIG.collections.documents,
      documentId
    );

    const notificationRecipients = [
      doc.studentId,
      ...(Array.isArray(doc.supervisorIds) ? doc.supervisorIds : []),
    ];

    await Promise.all(
      notificationRecipients.map((userId) =>
        databases.createDocument(
          APPWRITE_CONFIG.databaseId,
          APPWRITE_CONFIG.collections.notifications,
          ID.unique(),
          {
            userId,
            type: "submission",
            title: "Document Updated",
            message: `The document "${documentTitle}" has been updated`,
            read: false,
            relatedId: documentId,
            relatedType: "document",
          }
        )
      )
    );
  } catch (error) {
    console.error("Error notifying document participants:", error);
  }
}
