"use client";

import { useMemo, useCallback } from "react";
import { useAnalytics } from "@/hooks/useAnalytics";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { StatsCard } from "@/components/ui/stats-card";
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Pie<PERSON>hart,
  Pie,
  Cell,
  Legend,
  Area,
  AreaChart,
} from "recharts";
import {
  Users,
  FileText,
  BookOpen,
  AlertTriangle,
  CheckCircle2,
  Calendar,
  Target,
  BarChart3,
  Download,
  RefreshCw,
  TrendingUp,
  Activity,
  Bell,
  Shield,
  Zap,
  Clock,
  Award,
  Brain,
  Gauge,
  Heart,
  MessageSquare,
  Settings,
  Workflow,
} from "lucide-react";

interface ManagerAnalyticsViewProps {
  timeRange?: "month" | "quarter" | "year";
}

interface ManagerMetrics {
  totalUsers: number;
  activeUsers: number;
  totalProjects: number;
  activeProjects: number;
  completedProjects: number;
  pendingSupervisorProjects: number;
  totalDocuments: number;
  pendingReviews: number;
  totalMilestones: number;
  overdueMilestones: number;
  userEngagementRate: number;
  projectCompletionRate: number;
  documentApprovalRate: number;
  milestoneCompletionRate: number;
  supervisorWorkload: Array<{
    supervisorId: string;
    supervisorName: string;
    projectCount: number;
  }>;
  avgProjectsPerSupervisor: number;
}

interface ChartDataItem {
  name: string;
  value: number;
  color: string;
}

interface TrendDataItem {
  month: string;
  created: number;
  completed: number;
  active: number;
  completionRate: number;
}

// Enhanced color scheme for better accessibility and modern design
const STATUS_COLORS = {
  active: "hsl(142, 76%, 36%)", // Green
  completed: "hsl(221, 83%, 53%)", // Blue
  pending_supervisor: "hsl(38, 92%, 50%)", // Amber
  archived: "hsl(215, 25%, 35%)", // Gray
  suspended: "hsl(0, 84%, 60%)", // Red
  under_review: "hsl(262, 83%, 58%)", // Purple
  approved: "hsl(142, 76%, 36%)", // Green
  rejected: "hsl(0, 84%, 60%)", // Red
  draft: "hsl(215, 25%, 35%)", // Gray
} as const;

// Chart configuration for consistent styling
const CHART_CONFIG = {
  margin: { top: 5, right: 30, left: 20, bottom: 5 },
  animationDuration: 300,
  grid: {
    strokeDasharray: "3 3",
    stroke: "hsl(var(--border))",
    opacity: 0.3,
  },
  tooltip: {
    contentStyle: {
      backgroundColor: "hsl(var(--card))",
      border: "1px solid hsl(var(--border))",
      borderRadius: "var(--radius)",
      boxShadow: "0 4px 6px -1px rgb(0 0 0 / 0.1)",
    },
  },
} as const;

export function ManagerAnalyticsView({ timeRange = "year" }: ManagerAnalyticsViewProps) {
  const {
    analytics,
    isLoading,
    error,
    refreshAnalytics,
    exportAnalytics,
  } = useAnalytics({
    timeRange,
    enableRealTime: true,
    includeExtendedAnalytics: true,
    filters: { userRole: 'all' } // Managers see all data
  });

  // Calculate comprehensive manager metrics with proper error handling
  const managerMetrics = useMemo((): ManagerMetrics | null => {
    if (!analytics) return null;

    try {
      const totalUsers = analytics.userEngagement?.totalUsers || 0;
      const activeUsers = analytics.userEngagement?.activeUsers || 0;
      const totalProjects = analytics.totalProjects || 0;
      const activeProjects = analytics.projectsByStatus?.active || 0;
      const completedProjects = analytics.projectsByStatus?.completed || 0;
      const pendingSupervisorProjects = analytics.projectsByStatus?.pending_supervisor || 0;
      const totalDocuments = analytics.documentAnalytics?.totalDocuments || 0;
      const pendingReviews = analytics.documentAnalytics?.documentsByStatus?.under_review || 0;
      const totalMilestones = analytics.milestoneAnalytics?.totalMilestones || 0;
      const overdueMilestones = analytics.milestoneAnalytics?.overdueMilestones || 0;

      const userEngagementRate = totalUsers > 0 ? (activeUsers / totalUsers) * 100 : 0;
      const projectCompletionRate = totalProjects > 0 ? (completedProjects / totalProjects) * 100 : 0;
      const documentApprovalRate = analytics.documentAnalytics?.documentsCompletionRate || 0;
      const milestoneCompletionRate = analytics.milestoneAnalytics?.milestonesCompletionRate || 0;

      // Calculate supervisor workload distribution with safety checks
      const supervisorWorkload = analytics.supervisorLoad || [];
      const avgProjectsPerSupervisor = supervisorWorkload.length > 0 ?
        supervisorWorkload.reduce((sum, s) => sum + (s?.projectCount || 0), 0) / supervisorWorkload.length : 0;

      return {
        totalUsers,
        activeUsers,
        totalProjects,
        activeProjects,
        completedProjects,
        pendingSupervisorProjects,
        totalDocuments,
        pendingReviews,
        totalMilestones,
        overdueMilestones,
        userEngagementRate: Math.round(userEngagementRate * 10) / 10,
        projectCompletionRate: Math.round(projectCompletionRate * 10) / 10,
        documentApprovalRate: Math.round(documentApprovalRate * 10) / 10,
        milestoneCompletionRate: Math.round(milestoneCompletionRate * 10) / 10,
        supervisorWorkload,
        avgProjectsPerSupervisor: Math.round(avgProjectsPerSupervisor * 10) / 10,
      };
    } catch (error) {
      console.error('Error calculating manager metrics:', error);
      return null;
    }
  }, [analytics]);

  // Memoized chart data transformations
  const chartData = useMemo(() => {
    if (!analytics) return { projectStatusData: [], userRoleData: [], monthlyTrendData: [] };

    const projectStatusData: ChartDataItem[] = Object.entries(analytics.projectsByStatus || {}).map(([status, count]) => ({
      name: status.replace('_', ' ').toUpperCase(),
      value: count as number,
      color: STATUS_COLORS[status as keyof typeof STATUS_COLORS] || "hsl(215, 25%, 35%)"
    }));

    const userRoleData: ChartDataItem[] = Object.entries(analytics.userEngagement?.usersByRole || {}).map(([role, count]) => ({
      name: role.charAt(0).toUpperCase() + role.slice(1),
      value: count as number,
      color: role === 'student' ? 'hsl(221, 83%, 53%)' :
             role === 'supervisor' ? 'hsl(142, 76%, 36%)' :
             role === 'manager' ? 'hsl(38, 92%, 50%)' : 'hsl(262, 83%, 58%)'
    }));

    const monthlyTrendData: TrendDataItem[] = analytics.projectsByMonth?.map(month => ({
      month: month.shortMonth,
      created: month.count,
      completed: month.completed,
      active: month.active,
      completionRate: month.completionRate
    })) || [];

    return { projectStatusData, userRoleData, monthlyTrendData };
  }, [analytics]);

  // Export handler with error handling
  const handleExport = useCallback(() => {
    try {
      exportAnalytics('json');
    } catch (error) {
      console.error('Export failed:', error);
    }
  }, [exportAnalytics]);

  // Enhanced loading state with skeleton components
  if (isLoading) {
    return (
      <div className="space-y-6">
        {/* Header skeleton */}
        <div className="flex items-center justify-between">
          <div className="space-y-2">
            <div className="h-8 bg-muted rounded w-64 animate-pulse"></div>
            <div className="h-4 bg-muted rounded w-96 animate-pulse"></div>
          </div>
          <div className="flex gap-2">
            <div className="h-9 bg-muted rounded w-20 animate-pulse"></div>
            <div className="h-9 bg-muted rounded w-24 animate-pulse"></div>
          </div>
        </div>

        {/* Metrics cards skeleton */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-6">
          {[...Array(6)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <div className="h-4 bg-muted rounded w-20"></div>
                <div className="h-4 w-4 bg-muted rounded"></div>
              </CardHeader>
              <CardContent>
                <div className="h-8 bg-muted rounded w-16 mb-1"></div>
                <div className="h-3 bg-muted rounded w-24"></div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Charts skeleton */}
        <div className="grid gap-6 md:grid-cols-2">
          {[...Array(4)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader>
                <div className="h-6 bg-muted rounded w-48"></div>
                <div className="h-4 bg-muted rounded w-64"></div>
              </CardHeader>
              <CardContent>
                <div className="h-[300px] bg-muted rounded"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  // Enhanced error state
  if (error || !analytics || !managerMetrics) {
    return (
      <div className="space-y-6">
        <Card className="border-destructive/50 bg-destructive/5">
          <CardContent className="p-8">
            <div className="text-center">
              <AlertTriangle className="h-12 w-12 mx-auto mb-4 text-destructive" />
              <h3 className="text-lg font-semibold mb-2">Analytics Unavailable</h3>
              <p className="text-muted-foreground mb-4">
                {error ? 'Failed to load analytics data. Please try again.' : 'No analytics data available.'}
              </p>
              <div className="flex gap-2 justify-center">
                <Button variant="outline" onClick={refreshAnalytics}>
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Retry
                </Button>
                <Button variant="outline" onClick={handleExport}>
                  <Download className="h-4 w-4 mr-2" />
                  Export Raw Data
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Destructure chart data for cleaner code
  const { projectStatusData, userRoleData, monthlyTrendData } = chartData;

  return (
    <div className="space-y-6">
      {/* Enhanced Header */}
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div className="space-y-1">
          <h1 className="text-3xl font-bold tracking-tight">Manager Analytics</h1>
          <p className="text-muted-foreground">
            Comprehensive overview of organizational performance and key metrics
          </p>
        </div>
        <div className="flex items-center gap-3">
          <Badge variant="secondary" className="text-sm font-medium">
            {timeRange.charAt(0).toUpperCase() + timeRange.slice(1)} View
          </Badge>
          <Button variant="outline" size="sm" onClick={handleExport} className="gap-2">
            <Download className="h-4 w-4" />
            Export Data
          </Button>
          <Button variant="outline" size="sm" onClick={refreshAnalytics} className="gap-2">
            <RefreshCw className="h-4 w-4" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Executive Summary - Using StatsCard for consistency */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-6">
        <StatsCard
          title="Total Users"
          value={managerMetrics.totalUsers}
          icon={<Users className="h-4 w-4" />}
          description={`${managerMetrics.activeUsers} active (${managerMetrics.userEngagementRate}%)`}
          variant="default"
        />

        <StatsCard
          title="Total Projects"
          value={managerMetrics.totalProjects}
          icon={<BookOpen className="h-4 w-4" />}
          description={`${managerMetrics.activeProjects} active, ${managerMetrics.completedProjects} completed`}
          variant="default"
        />

        <StatsCard
          title="Pending Assignment"
          value={managerMetrics.pendingSupervisorProjects}
          icon={<Calendar className="h-4 w-4" />}
          description="projects need supervisors"
          variant={managerMetrics.pendingSupervisorProjects > 0 ? "warning" : "default"}
        />

        <StatsCard
          title="Documents"
          value={managerMetrics.totalDocuments}
          icon={<FileText className="h-4 w-4" />}
          description={`${managerMetrics.pendingReviews} pending review`}
          variant="default"
        />

        <StatsCard
          title="Completion Rate"
          value={`${managerMetrics.projectCompletionRate}%`}
          icon={<Target className="h-4 w-4" />}
          description="project completion"
          variant={managerMetrics.projectCompletionRate >= 80 ? "success" :
                  managerMetrics.projectCompletionRate >= 60 ? "warning" : "danger"}
        />

        <StatsCard
          title={managerMetrics.overdueMilestones > 0 ? "Overdue Items" : "On Track"}
          value={managerMetrics.overdueMilestones > 0 ? managerMetrics.overdueMilestones : "✓"}
          icon={managerMetrics.overdueMilestones > 0 ?
                <AlertTriangle className="h-4 w-4" /> :
                <CheckCircle2 className="h-4 w-4" />}
          description={managerMetrics.overdueMilestones > 0 ? "milestones overdue" : "All on schedule"}
          variant={managerMetrics.overdueMilestones > 0 ? "danger" : "success"}
        />
      </div>

      {/* Performance Overview */}
      <div className="grid gap-6 md:grid-cols-2">
        <Card className="shadow-sm">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5 text-primary" />
              Project Trends Over Time
            </CardTitle>
            <CardDescription>Monthly project creation and completion trends</CardDescription>
          </CardHeader>
          <CardContent>
            {monthlyTrendData.length > 0 ? (
              <div className="h-[320px]">
                <ResponsiveContainer width="100%" height="100%">
                  <AreaChart data={monthlyTrendData} margin={CHART_CONFIG.margin}>
                    <CartesianGrid
                      strokeDasharray={CHART_CONFIG.grid.strokeDasharray}
                      stroke={CHART_CONFIG.grid.stroke}
                      opacity={CHART_CONFIG.grid.opacity}
                    />
                    <XAxis
                      dataKey="month"
                      tick={{ fontSize: 12 }}
                      axisLine={{ stroke: 'hsl(var(--border))' }}
                    />
                    <YAxis
                      tick={{ fontSize: 12 }}
                      axisLine={{ stroke: 'hsl(var(--border))' }}
                    />
                    <Tooltip
                      contentStyle={CHART_CONFIG.tooltip.contentStyle}
                      labelStyle={{ color: 'hsl(var(--foreground))' }}
                    />
                    <Legend />
                    <Area
                      type="monotone"
                      dataKey="created"
                      stackId="1"
                      stroke="hsl(221, 83%, 53%)"
                      fill="hsl(221, 83%, 53%)"
                      fillOpacity={0.6}
                      name="Created"
                      animationDuration={CHART_CONFIG.animationDuration}
                    />
                    <Area
                      type="monotone"
                      dataKey="completed"
                      stackId="1"
                      stroke="hsl(142, 76%, 36%)"
                      fill="hsl(142, 76%, 36%)"
                      fillOpacity={0.6}
                      name="Completed"
                      animationDuration={CHART_CONFIG.animationDuration}
                    />
                  </AreaChart>
                </ResponsiveContainer>
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center py-12 text-muted-foreground">
                <BarChart3 className="h-12 w-12 mb-3 opacity-50" />
                <p className="text-sm font-medium">No trend data available</p>
                <p className="text-xs">Data will appear as projects are created</p>
              </div>
            )}
          </CardContent>
        </Card>

        <Card className="shadow-sm">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5 text-primary" />
              Supervisor Workload Distribution
            </CardTitle>
            <CardDescription>
              Project distribution across supervisors (Avg: {managerMetrics.avgProjectsPerSupervisor} projects/supervisor)
            </CardDescription>
          </CardHeader>
          <CardContent>
            {managerMetrics.supervisorWorkload.length > 0 ? (
              <div className="h-[320px]">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart
                    data={managerMetrics.supervisorWorkload}
                    layout="vertical"
                    margin={CHART_CONFIG.margin}
                  >
                    <CartesianGrid
                      strokeDasharray={CHART_CONFIG.grid.strokeDasharray}
                      stroke={CHART_CONFIG.grid.stroke}
                      opacity={CHART_CONFIG.grid.opacity}
                    />
                    <XAxis
                      type="number"
                      tick={{ fontSize: 12 }}
                      axisLine={{ stroke: 'hsl(var(--border))' }}
                    />
                    <YAxis
                      dataKey="supervisorName"
                      type="category"
                      width={120}
                      tick={{ fontSize: 12 }}
                      axisLine={{ stroke: 'hsl(var(--border))' }}
                    />
                    <Tooltip
                      contentStyle={CHART_CONFIG.tooltip.contentStyle}
                      labelStyle={{ color: 'hsl(var(--foreground))' }}
                    />
                    <Bar
                      dataKey="projectCount"
                      fill="hsl(262, 83%, 58%)"
                      radius={[0, 4, 4, 0]}
                      animationDuration={CHART_CONFIG.animationDuration}
                    />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center py-12 text-muted-foreground">
                <Users className="h-12 w-12 mb-3 opacity-50" />
                <p className="text-sm font-medium">No supervisor data available</p>
                <p className="text-xs">Assign supervisors to projects to see distribution</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Status Distributions */}
      <div className="grid gap-6 md:grid-cols-3">
        <Card className="shadow-sm">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5 text-primary" />
              Project Status Distribution
            </CardTitle>
            <CardDescription>Current status breakdown of all projects</CardDescription>
          </CardHeader>
          <CardContent>
            {projectStatusData.length > 0 ? (
              <div className="h-[280px]">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={projectStatusData}
                      cx="50%"
                      cy="50%"
                      outerRadius={90}
                      dataKey="value"
                      label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                      labelLine={false}
                      animationDuration={CHART_CONFIG.animationDuration}
                    >
                      {projectStatusData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip
                      contentStyle={CHART_CONFIG.tooltip.contentStyle}
                      labelStyle={{ color: 'hsl(var(--foreground))' }}
                    />
                  </PieChart>
                </ResponsiveContainer>
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center py-12 text-muted-foreground">
                <BarChart3 className="h-12 w-12 mb-3 opacity-50" />
                <p className="text-sm font-medium">No project data available</p>
              </div>
            )}
          </CardContent>
        </Card>

        <Card className="shadow-sm">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5 text-primary" />
              User Role Distribution
            </CardTitle>
            <CardDescription>Breakdown of users by role</CardDescription>
          </CardHeader>
          <CardContent>
            {userRoleData.length > 0 ? (
              <div className="h-[280px]">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={userRoleData}
                      cx="50%"
                      cy="50%"
                      outerRadius={90}
                      dataKey="value"
                      label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                      labelLine={false}
                      animationDuration={CHART_CONFIG.animationDuration}
                    >
                      {userRoleData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip
                      contentStyle={CHART_CONFIG.tooltip.contentStyle}
                      labelStyle={{ color: 'hsl(var(--foreground))' }}
                    />
                  </PieChart>
                </ResponsiveContainer>
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center py-12 text-muted-foreground">
                <Users className="h-12 w-12 mb-3 opacity-50" />
                <p className="text-sm font-medium">No user data available</p>
              </div>
            )}
          </CardContent>
        </Card>

        <Card className="shadow-sm">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Target className="h-5 w-5 text-primary" />
              Key Performance Indicators
            </CardTitle>
            <CardDescription>Critical metrics at a glance</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              <div className="flex items-center justify-between p-3 rounded-lg bg-muted/30">
                <span className="text-sm font-medium text-muted-foreground">User Engagement</span>
                <span className="text-xl font-bold text-primary">
                  {managerMetrics.userEngagementRate}%
                </span>
              </div>
              <div className="flex items-center justify-between p-3 rounded-lg bg-muted/30">
                <span className="text-sm font-medium text-muted-foreground">Project Completion</span>
                <span className="text-xl font-bold text-green-600 dark:text-green-400">
                  {managerMetrics.projectCompletionRate}%
                </span>
              </div>
              <div className="flex items-center justify-between p-3 rounded-lg bg-muted/30">
                <span className="text-sm font-medium text-muted-foreground">Document Approval</span>
                <span className="text-xl font-bold text-purple-600 dark:text-purple-400">
                  {managerMetrics.documentApprovalRate}%
                </span>
              </div>
              <div className="flex items-center justify-between p-3 rounded-lg bg-muted/30">
                <span className="text-sm font-medium text-muted-foreground">Milestone Completion</span>
                <span className="text-xl font-bold text-orange-600 dark:text-orange-400">
                  {managerMetrics.milestoneCompletionRate}%
                </span>
              </div>
              <div className="flex items-center justify-between p-3 rounded-lg bg-muted/30">
                <span className="text-sm font-medium text-muted-foreground">Avg Projects/Supervisor</span>
                <span className="text-xl font-bold text-indigo-600 dark:text-indigo-400">
                  {managerMetrics.avgProjectsPerSupervisor}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Seasonal Trends and Growth Metrics */}
      {analytics?.trendAnalysis && (
        <div className="grid gap-6 md:grid-cols-2">
          <Card className="shadow-sm">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5 text-primary" />
                Seasonal Activity Trends
              </CardTitle>
              <CardDescription>Quarterly activity patterns across the year</CardDescription>
            </CardHeader>
            <CardContent>
              {analytics.trendAnalysis.seasonalTrends.length > 0 ? (
                <div className="h-[280px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart data={analytics.trendAnalysis.seasonalTrends} margin={CHART_CONFIG.margin}>
                      <CartesianGrid
                        strokeDasharray={CHART_CONFIG.grid.strokeDasharray}
                        stroke={CHART_CONFIG.grid.stroke}
                        opacity={CHART_CONFIG.grid.opacity}
                      />
                      <XAxis
                        dataKey="period"
                        tick={{ fontSize: 12 }}
                        axisLine={{ stroke: 'hsl(var(--border))' }}
                      />
                      <YAxis
                        tick={{ fontSize: 12 }}
                        axisLine={{ stroke: 'hsl(var(--border))' }}
                      />
                      <Tooltip
                        contentStyle={CHART_CONFIG.tooltip.contentStyle}
                        labelStyle={{ color: 'hsl(var(--foreground))' }}
                      />
                      <Legend />
                      <Bar
                        dataKey="projectActivity"
                        name="Project Activity (%)"
                        fill="hsl(221, 83%, 53%)"
                        radius={[2, 2, 0, 0]}
                        animationDuration={CHART_CONFIG.animationDuration}
                      />
                      <Bar
                        dataKey="documentActivity"
                        name="Document Activity (%)"
                        fill="hsl(142, 76%, 36%)"
                        radius={[2, 2, 0, 0]}
                        animationDuration={CHART_CONFIG.animationDuration}
                      />
                      <Bar
                        dataKey="userActivity"
                        name="User Activity (%)"
                        fill="hsl(262, 83%, 58%)"
                        radius={[2, 2, 0, 0]}
                        animationDuration={CHART_CONFIG.animationDuration}
                      />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              ) : (
                <div className="flex flex-col items-center justify-center py-12 text-muted-foreground">
                  <Calendar className="h-12 w-12 mb-3 opacity-50" />
                  <p className="text-sm font-medium">No seasonal data available</p>
                  <p className="text-xs">Data will appear as activity accumulates</p>
                </div>
              )}
            </CardContent>
          </Card>

          <Card className="shadow-sm">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5 text-primary" />
                Growth Metrics & Insights
              </CardTitle>
              <CardDescription>Month-over-month growth rates and predictions</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div className="grid grid-cols-1 gap-4">
                  <div className="flex items-center justify-between p-4 rounded-lg bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-950 dark:to-blue-900">
                    <div>
                      <p className="text-sm font-medium text-blue-700 dark:text-blue-300">Project Growth</p>
                      <p className="text-xs text-blue-600 dark:text-blue-400">Month-over-month</p>
                    </div>
                    <div className="text-right">
                      <span className="text-2xl font-bold text-blue-700 dark:text-blue-300">
                        {analytics.trendAnalysis.growthMetrics.projectGrowthRate > 0 ? '+' : ''}
                        {analytics.trendAnalysis.growthMetrics.projectGrowthRate}%
                      </span>
                    </div>
                  </div>

                  <div className="flex items-center justify-between p-4 rounded-lg bg-gradient-to-r from-green-50 to-green-100 dark:from-green-950 dark:to-green-900">
                    <div>
                      <p className="text-sm font-medium text-green-700 dark:text-green-300">User Growth</p>
                      <p className="text-xs text-green-600 dark:text-green-400">Active users</p>
                    </div>
                    <div className="text-right">
                      <span className="text-2xl font-bold text-green-700 dark:text-green-300">
                        {analytics.trendAnalysis.growthMetrics.userGrowthRate > 0 ? '+' : ''}
                        {analytics.trendAnalysis.growthMetrics.userGrowthRate}%
                      </span>
                    </div>
                  </div>

                  <div className="flex items-center justify-between p-4 rounded-lg bg-gradient-to-r from-purple-50 to-purple-100 dark:from-purple-950 dark:to-purple-900">
                    <div>
                      <p className="text-sm font-medium text-purple-700 dark:text-purple-300">Document Growth</p>
                      <p className="text-xs text-purple-600 dark:text-purple-400">New submissions</p>
                    </div>
                    <div className="text-right">
                      <span className="text-2xl font-bold text-purple-700 dark:text-purple-300">
                        {analytics.trendAnalysis.growthMetrics.documentGrowthRate > 0 ? '+' : ''}
                        {analytics.trendAnalysis.growthMetrics.documentGrowthRate}%
                      </span>
                    </div>
                  </div>
                </div>

                {analytics.trendAnalysis.predictiveInsights.recommendedActions.length > 0 && (
                  <div className="mt-6 p-4 rounded-lg bg-amber-50 dark:bg-amber-950 border border-amber-200 dark:border-amber-800">
                    <h4 className="text-sm font-semibold text-amber-800 dark:text-amber-200 mb-2">
                      Recommended Actions
                    </h4>
                    <ul className="space-y-1">
                      {analytics.trendAnalysis.predictiveInsights.recommendedActions.map((action, index) => (
                        <li key={index} className="text-xs text-amber-700 dark:text-amber-300 flex items-center gap-2">
                          <span className="w-1 h-1 bg-amber-600 dark:bg-amber-400 rounded-full"></span>
                          {action}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Project Health Dashboard */}
      {analytics?.projectHealth && (
        <div className="space-y-6">
          <div className="flex items-center gap-2 mb-4">
            <Heart className="h-6 w-6 text-primary" />
            <h2 className="text-2xl font-bold">Project Health & Performance</h2>
          </div>

          <div className="grid gap-6 md:grid-cols-3">
            <Card className="shadow-sm">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Activity className="h-5 w-5 text-green-600" />
                  Project Health Status
                </CardTitle>
                <CardDescription>Current health distribution of all projects</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between p-3 rounded-lg bg-green-50 dark:bg-green-950">
                    <div className="flex items-center gap-2">
                      <CheckCircle2 className="h-4 w-4 text-green-600" />
                      <span className="text-sm font-medium">Healthy</span>
                    </div>
                    <span className="text-lg font-bold text-green-600">
                      {analytics.projectHealth.healthyProjects}
                    </span>
                  </div>
                  <div className="flex items-center justify-between p-3 rounded-lg bg-amber-50 dark:bg-amber-950">
                    <div className="flex items-center gap-2">
                      <Clock className="h-4 w-4 text-amber-600" />
                      <span className="text-sm font-medium">At Risk</span>
                    </div>
                    <span className="text-lg font-bold text-amber-600">
                      {analytics.projectHealth.atRiskProjects}
                    </span>
                  </div>
                  <div className="flex items-center justify-between p-3 rounded-lg bg-red-50 dark:bg-red-950">
                    <div className="flex items-center gap-2">
                      <AlertTriangle className="h-4 w-4 text-red-600" />
                      <span className="text-sm font-medium">Critical</span>
                    </div>
                    <span className="text-lg font-bold text-red-600">
                      {analytics.projectHealth.criticalProjects}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="shadow-sm">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Zap className="h-5 w-5 text-blue-600" />
                  Project Velocity
                </CardTitle>
                <CardDescription>Completion rate and project throughput</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-blue-600 mb-1">
                      {analytics.projectHealth.projectVelocity}
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Projects completed per {timeRange}
                    </p>
                  </div>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Success Rate</span>
                      <span className="font-medium">
                        {analytics.projectHealth.qualityMetrics.projectSuccessRate.toFixed(1)}%
                      </span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>First-time Approval</span>
                      <span className="font-medium">
                        {analytics.projectHealth.qualityMetrics.firstTimeApprovalRate.toFixed(1)}%
                      </span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="shadow-sm">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Award className="h-5 w-5 text-purple-600" />
                  Quality Metrics
                </CardTitle>
                <CardDescription>Document and project quality indicators</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between p-3 rounded-lg bg-purple-50 dark:bg-purple-950">
                    <span className="text-sm font-medium">Avg. Revisions</span>
                    <span className="text-lg font-bold text-purple-600">
                      {analytics.projectHealth.qualityMetrics.averageDocumentRevisions}
                    </span>
                  </div>
                  <div className="flex items-center justify-between p-3 rounded-lg bg-indigo-50 dark:bg-indigo-950">
                    <span className="text-sm font-medium">Approval Rate</span>
                    <span className="text-lg font-bold text-indigo-600">
                      {managerMetrics.documentApprovalRate}%
                    </span>
                  </div>
                  <div className="flex items-center justify-between p-3 rounded-lg bg-teal-50 dark:bg-teal-950">
                    <span className="text-sm font-medium">Milestone Rate</span>
                    <span className="text-lg font-bold text-teal-600">
                      {managerMetrics.milestoneCompletionRate}%
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      )}

      {/* Notification Analytics */}
      {analytics?.notificationAnalytics && (
        <div className="space-y-6">
          <div className="flex items-center gap-2 mb-4">
            <Bell className="h-6 w-6 text-primary" />
            <h2 className="text-2xl font-bold">Communication & Notifications</h2>
          </div>

          <div className="grid gap-6 md:grid-cols-2">
            <Card className="shadow-sm">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MessageSquare className="h-5 w-5 text-blue-600" />
                  Notification Overview
                </CardTitle>
                <CardDescription>Communication effectiveness and response metrics</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="text-center p-3 rounded-lg bg-blue-50 dark:bg-blue-950">
                      <div className="text-2xl font-bold text-blue-600">
                        {analytics.notificationAnalytics.totalNotifications}
                      </div>
                      <p className="text-xs text-blue-600">Total Sent</p>
                    </div>
                    <div className="text-center p-3 rounded-lg bg-green-50 dark:bg-green-950">
                      <div className="text-2xl font-bold text-green-600">
                        {analytics.notificationAnalytics.responseRate}%
                      </div>
                      <p className="text-xs text-green-600">Response Rate</p>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Unread Notifications</span>
                      <span className="font-medium text-amber-600">
                        {analytics.notificationAnalytics.unreadNotifications}
                      </span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Avg Response Time</span>
                      <span className="font-medium">
                        {analytics.notificationAnalytics.averageResponseTime}h
                      </span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="shadow-sm">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5 text-purple-600" />
                  Notification Types
                </CardTitle>
                <CardDescription>Breakdown by notification category</CardDescription>
              </CardHeader>
              <CardContent>
                {Object.keys(analytics.notificationAnalytics.notificationsByType).length > 0 ? (
                  <div className="space-y-3">
                    {Object.entries(analytics.notificationAnalytics.notificationsByType).map(([type, count]) => (
                      <div key={type} className="flex items-center justify-between p-2 rounded-lg bg-muted/30">
                        <span className="text-sm font-medium capitalize">{type.replace('_', ' ')}</span>
                        <div className="flex items-center gap-2">
                          <span className="text-sm font-bold">{count}</span>
                          <div className="w-16 h-2 bg-muted rounded-full overflow-hidden">
                            <div
                              className="h-full bg-purple-500 rounded-full"
                              style={{
                                width: `${(count / analytics.notificationAnalytics.totalNotifications) * 100}%`
                              }}
                            />
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="flex flex-col items-center justify-center py-8 text-muted-foreground">
                    <Bell className="h-8 w-8 mb-2 opacity-50" />
                    <p className="text-sm">No notification data available</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          <Card className="shadow-sm">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5 text-indigo-600" />
                Communication Trends
              </CardTitle>
              <CardDescription>Monthly notification activity and engagement patterns</CardDescription>
            </CardHeader>
            <CardContent>
              {analytics.notificationAnalytics.notificationTrends.length > 0 ? (
                <div className="h-[300px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <AreaChart data={analytics.notificationAnalytics.notificationTrends} margin={CHART_CONFIG.margin}>
                      <CartesianGrid
                        strokeDasharray={CHART_CONFIG.grid.strokeDasharray}
                        stroke={CHART_CONFIG.grid.stroke}
                        opacity={CHART_CONFIG.grid.opacity}
                      />
                      <XAxis
                        dataKey="month"
                        tick={{ fontSize: 12 }}
                        axisLine={{ stroke: 'hsl(var(--border))' }}
                      />
                      <YAxis
                        tick={{ fontSize: 12 }}
                        axisLine={{ stroke: 'hsl(var(--border))' }}
                      />
                      <Tooltip
                        contentStyle={CHART_CONFIG.tooltip.contentStyle}
                        labelStyle={{ color: 'hsl(var(--foreground))' }}
                      />
                      <Legend />
                      <Area
                        type="monotone"
                        dataKey="sent"
                        stackId="1"
                        stroke="hsl(221, 83%, 53%)"
                        fill="hsl(221, 83%, 53%)"
                        fillOpacity={0.6}
                        name="Sent"
                        animationDuration={CHART_CONFIG.animationDuration}
                      />
                      <Area
                        type="monotone"
                        dataKey="read"
                        stackId="1"
                        stroke="hsl(142, 76%, 36%)"
                        fill="hsl(142, 76%, 36%)"
                        fillOpacity={0.6}
                        name="Read"
                        animationDuration={CHART_CONFIG.animationDuration}
                      />
                      <Area
                        type="monotone"
                        dataKey="responded"
                        stackId="1"
                        stroke="hsl(262, 83%, 58%)"
                        fill="hsl(262, 83%, 58%)"
                        fillOpacity={0.6}
                        name="Responded"
                        animationDuration={CHART_CONFIG.animationDuration}
                      />
                    </AreaChart>
                  </ResponsiveContainer>
                </div>
              ) : (
                <div className="flex flex-col items-center justify-center py-12 text-muted-foreground">
                  <TrendingUp className="h-12 w-12 mb-3 opacity-50" />
                  <p className="text-sm font-medium">No trend data available</p>
                  <p className="text-xs">Communication trends will appear as data accumulates</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      )}

      {/* Resource Management Analytics */}
      {analytics?.projectHealth?.resourceUtilization && (
        <div className="space-y-6">
          <div className="flex items-center gap-2 mb-4">
            <Workflow className="h-6 w-6 text-primary" />
            <h2 className="text-2xl font-bold">Resource Management & Utilization</h2>
          </div>

          <div className="grid gap-6 md:grid-cols-2">
            <Card className="shadow-sm">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5 text-green-600" />
                  Supervisor Workload Analysis
                </CardTitle>
                <CardDescription>Detailed supervisor capacity and distribution</CardDescription>
              </CardHeader>
              <CardContent>
                {Object.keys(analytics.projectHealth.resourceUtilization.supervisorWorkload).length > 0 ? (
                  <div className="space-y-3">
                    {Object.entries(analytics.projectHealth.resourceUtilization.supervisorWorkload)
                      .sort(([,a], [,b]) => b - a)
                      .map(([name, workload]) => (
                      <div key={name} className="flex items-center justify-between p-3 rounded-lg bg-muted/30">
                        <span className="text-sm font-medium">{name}</span>
                        <div className="flex items-center gap-2">
                          <span className="text-sm font-bold">{workload} projects</span>
                          <div className="w-20 h-2 bg-muted rounded-full overflow-hidden">
                            <div
                              className="h-full bg-green-500 rounded-full"
                              style={{
                                width: `${Math.min((workload / Math.max(...Object.values(analytics.projectHealth.resourceUtilization.supervisorWorkload))) * 100, 100)}%`
                              }}
                            />
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="flex flex-col items-center justify-center py-8 text-muted-foreground">
                    <Users className="h-8 w-8 mb-2 opacity-50" />
                    <p className="text-sm">No supervisor workload data</p>
                  </div>
                )}
              </CardContent>
            </Card>

            <Card className="shadow-sm">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BookOpen className="h-5 w-5 text-blue-600" />
                  Student Project Distribution
                </CardTitle>
                <CardDescription>Student engagement and project allocation</CardDescription>
              </CardHeader>
              <CardContent>
                {Object.keys(analytics.projectHealth.resourceUtilization.studentWorkload).length > 0 ? (
                  <div className="space-y-3">
                    {Object.entries(analytics.projectHealth.resourceUtilization.studentWorkload)
                      .sort(([,a], [,b]) => b - a)
                      .slice(0, 8) // Show top 8 students
                      .map(([name, workload]) => (
                      <div key={name} className="flex items-center justify-between p-3 rounded-lg bg-muted/30">
                        <span className="text-sm font-medium">{name}</span>
                        <div className="flex items-center gap-2">
                          <span className="text-sm font-bold">{workload} projects</span>
                          <div className="w-16 h-2 bg-muted rounded-full overflow-hidden">
                            <div
                              className="h-full bg-blue-500 rounded-full"
                              style={{
                                width: `${Math.min((workload / Math.max(...Object.values(analytics.projectHealth.resourceUtilization.studentWorkload))) * 100, 100)}%`
                              }}
                            />
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="flex flex-col items-center justify-center py-8 text-muted-foreground">
                    <BookOpen className="h-8 w-8 mb-2 opacity-50" />
                    <p className="text-sm">No student workload data</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          <Card className="shadow-sm">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="h-5 w-5 text-orange-600" />
                Capacity Planning Insights
              </CardTitle>
              <CardDescription>Resource optimization recommendations and team metrics</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-6 md:grid-cols-3">
                <div className="text-center p-4 rounded-lg bg-orange-50 dark:bg-orange-950">
                  <div className="text-2xl font-bold text-orange-600 mb-1">
                    {analytics.projectHealth.resourceUtilization.averageTeamSize}
                  </div>
                  <p className="text-sm text-orange-600">Avg Team Size</p>
                </div>
                <div className="text-center p-4 rounded-lg bg-purple-50 dark:bg-purple-950">
                  <div className="text-2xl font-bold text-purple-600 mb-1">
                    {managerMetrics.avgProjectsPerSupervisor}
                  </div>
                  <p className="text-sm text-purple-600">Projects/Supervisor</p>
                </div>
                <div className="text-center p-4 rounded-lg bg-teal-50 dark:bg-teal-950">
                  <div className="text-2xl font-bold text-teal-600 mb-1">
                    {Math.round((managerMetrics.activeProjects / managerMetrics.totalUsers) * 100) / 100}
                  </div>
                  <p className="text-sm text-teal-600">Projects/User Ratio</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* System Performance & Health */}
      {analytics?.performanceMetrics && (
        <div className="space-y-6">
          <div className="flex items-center gap-2 mb-4">
            <Gauge className="h-6 w-6 text-primary" />
            <h2 className="text-2xl font-bold">System Performance & Health</h2>
          </div>

          <div className="grid gap-6 md:grid-cols-3">
            <Card className="shadow-sm">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Shield className="h-5 w-5 text-green-600" />
                  Data Quality
                </CardTitle>
                <CardDescription>System data integrity and completeness</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-green-600 mb-1">
                      {analytics.performanceMetrics.systemHealth.dataQuality}%
                    </div>
                    <p className="text-sm text-muted-foreground">Data Completeness</p>
                  </div>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Error Rate</span>
                      <span className="font-medium text-red-600">
                        {analytics.performanceMetrics.systemHealth.errorRate}%
                      </span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Update Frequency</span>
                      <span className="font-medium">
                        {analytics.performanceMetrics.systemHealth.updateFrequency}h
                      </span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="shadow-sm">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Clock className="h-5 w-5 text-blue-600" />
                  Data Freshness
                </CardTitle>
                <CardDescription>Real-time data synchronization status</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="text-center">
                    <div className="text-sm font-medium text-blue-600 mb-1">Last Updated</div>
                    <div className="text-lg font-bold">
                      {new Date(analytics.performanceMetrics.dataFreshness).toLocaleTimeString()}
                    </div>
                    <p className="text-xs text-muted-foreground">
                      {new Date(analytics.performanceMetrics.dataFreshness).toLocaleDateString()}
                    </p>
                  </div>
                  <div className="flex items-center justify-center">
                    <div className="flex items-center gap-2 px-3 py-1 rounded-full bg-green-100 dark:bg-green-900">
                      <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                      <span className="text-xs font-medium text-green-700 dark:text-green-300">Live Data</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="shadow-sm">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Brain className="h-5 w-5 text-purple-600" />
                  Predictive Insights
                </CardTitle>
                <CardDescription>AI-powered recommendations and forecasts</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {analytics.trendAnalysis?.predictiveInsights && (
                    <>
                      <div className="text-center p-3 rounded-lg bg-purple-50 dark:bg-purple-950">
                        <div className="text-lg font-bold text-purple-600">
                          {analytics.trendAnalysis.predictiveInsights.projectedCompletions}
                        </div>
                        <p className="text-xs text-purple-600">Projected Completions</p>
                      </div>
                      {analytics.trendAnalysis.predictiveInsights.estimatedBottlenecks.length > 0 && (
                        <div className="space-y-2">
                          <p className="text-xs font-medium text-muted-foreground">Potential Bottlenecks:</p>
                          {analytics.trendAnalysis.predictiveInsights.estimatedBottlenecks.map((bottleneck, index) => (
                            <div key={index} className="flex items-center gap-2 text-xs text-amber-600">
                              <AlertTriangle className="h-3 w-3" />
                              {bottleneck}
                            </div>
                          ))}
                        </div>
                      )}
                    </>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      )}
    </div>
  );
}
